<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue'
import * as d3 from 'd3'
import { AnalysisNode, NodeConnection } from '../utils/systemicThinking'

interface DeductiveNode extends AnalysisNode {
  children?: DeductiveNode[];
  parent?: DeductiveNode;
  depth?: number;
  x?: number;
  y?: number;
}

interface Props {
  nodes: AnalysisNode[];
  connections: NodeConnection[];
}

const props = defineProps<Props>()

const svgRef = ref<SVGElement | null>(null)
const currentStep = ref(0)
const history = ref<DeductiveNode[]>([])
const maxSteps = ref(0)

const width = 1200
const height = 800

const renderTree = (rootNode: DeductiveNode | null) => {
  console.log('DeductiveTreeGraph: Rendering tree with rootNode:', rootNode);
  if (!svgRef.value || !rootNode) {
    console.warn('DeductiveTreeGraph: Cannot render - missing svgRef or rootNode');
    return
  }

  try {
    d3.select(svgRef.value).selectAll('*').remove()

    const svg = d3.select(svgRef.value)
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(40, ${height / 2})`)

    const treeLayout = d3.tree<DeductiveNode>()
      .size([height - 100, width - 160])

    const root = d3.hierarchy(rootNode, d => d.children)
    console.log('DeductiveTreeGraph: D3 hierarchy created:', root);

    treeLayout(root)

    // Links
    const links = svg.selectAll('.link')
      .data(root.links())
      .enter().append('path')
      .attr('class', 'link')
      .attr('fill', 'none')
      .attr('stroke', '#ccc')
      .attr('stroke-width', 1.5)
      .attr('d', d3.linkHorizontal<d3.HierarchyPointLink<DeductiveNode>, DeductiveNode>()
        .x(d => d.y!)
        .y(d => d.x!)
      )

    console.log('DeductiveTreeGraph: Links rendered:', links.size());

    // Nodes
    const nodes = svg.selectAll('.node')
      .data(root.descendants())
      .enter().append('g')
      .attr('class', d => 'node' + (d.children ? ' node--internal' : ' node--leaf'))
      .attr('transform', d => `translate(${d.y},${d.x})`)

    nodes.append('circle')
      .attr('r', 8)
      .attr('fill', d => {
        if (d.data.type === 'initial') return '#64748b'
        if (d.data.type === 'positive') return '#22c55e'
        if (d.data.type === 'negative') return '#ef4444'
        return '#555'
      })
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)

    nodes.append('text')
      .attr('dy', '.35em')
      .attr('x', d => d.children ? -13 : 13)
      .attr('text-anchor', d => d.children ? 'end' : 'start')
      .text(d => d.data.title)
      .attr('fill', '#333')
      .attr('font-size', '12px')
      .style('font-weight', 'bold')

    console.log('DeductiveTreeGraph: Nodes rendered:', nodes.size());
  } catch (error) {
    console.error('DeductiveTreeGraph: Error rendering tree:', error);
  }
}

const buildTree = (nodes: AnalysisNode[], connections: NodeConnection[]): DeductiveNode | null => {
  console.log('DeductiveTreeGraph: Building tree with nodes:', nodes, 'and connections:', connections);
  const nodeMap = new Map<string, DeductiveNode>()
  nodes.forEach(n => nodeMap.set(n.id, { ...n }))

  // Find the initial node - look for node with type 'initial' or level 0
  let initialNode = Array.from(nodeMap.values()).find(n => n.type === 'initial' || n.level === 0)

  // Fallback: look for 'initial-0' specifically
  if (!initialNode) {
    initialNode = nodeMap.get('initial-0')
  }

  // If still not found, use the first node as root
  if (!initialNode && nodes.length > 0) {
    initialNode = nodeMap.get(nodes[0].id)
    console.warn('DeductiveTreeGraph: No initial node found, using first node as root:', initialNode)
  }

  if (!initialNode) {
    console.error("DeductiveTreeGraph: No suitable root node found.")
    return null
  }

  // Establish parent-child relationships
  connections.forEach(conn => {
    const parent = nodeMap.get(conn.from)
    const child = nodeMap.get(conn.to)
    if (parent && child) {
      if (!parent.children) {
        parent.children = []
      }
      parent.children.push(child)
      child.parent = parent // For reverse lookup if needed
    }
  })
  console.log('DeductiveTreeGraph: Built rootNode:', initialNode);
  return initialNode
}

// 创建时间线步骤
const createTimelineSteps = (rootNode: DeductiveNode): DeductiveNode[] => {
  const steps: DeductiveNode[] = []

  // 步骤1: 只显示根节点
  const step1 = { ...rootNode, children: undefined }
  steps.push(step1)

  // 步骤2: 显示根节点和第一层子节点
  if (rootNode.children && rootNode.children.length > 0) {
    const step2 = {
      ...rootNode,
      children: rootNode.children.map(child => ({ ...child, children: undefined }))
    }
    steps.push(step2)

    // 步骤3: 显示完整的树
    steps.push(rootNode)
  }

  return steps
}

watch([() => props.nodes, () => props.connections], ([newNodes, newConnections]) => {
  if (newNodes.length > 0) {
    const rootNode = buildTree(newNodes, newConnections)
    if (rootNode) {
      history.value = createTimelineSteps(rootNode)
      maxSteps.value = history.value.length
      currentStep.value = 0
      renderTree(history.value[currentStep.value])
    }
  }
}, { immediate: true, deep: true })

const canGoBack = computed(() => currentStep.value > 0)
const canGoForward = computed(() => currentStep.value < history.value.length - 1)

const navigateHistory = (step: number) => {
  if (step >= 0 && step < history.value.length) {
    currentStep.value = step
    const rootNode = history.value[currentStep.value]
    if (rootNode) {
      renderTree(rootNode)
    }
  }
}

const goBack = () => {
  if (canGoBack.value) {
    navigateHistory(currentStep.value - 1)
  }
}

const goForward = () => {
  if (canGoForward.value) {
    navigateHistory(currentStep.value + 1)
  }
}

// 自动播放功能
const autoPlay = ref(false)
const autoPlayInterval = ref<number | null>(null)

const startAutoPlay = () => {
  if (autoPlayInterval.value) return

  autoPlay.value = true
  autoPlayInterval.value = window.setInterval(() => {
    if (canGoForward.value) {
      goForward()
    } else {
      stopAutoPlay()
    }
  }, 2000)
}

const stopAutoPlay = () => {
  if (autoPlayInterval.value) {
    clearInterval(autoPlayInterval.value)
    autoPlayInterval.value = null
  }
  autoPlay.value = false
}

onMounted(() => {
  // Initial render if data is already available
  if (props.nodes.length > 0) {
    const rootNode = buildTree(props.nodes, props.connections)
    if (rootNode) {
      history.value = createTimelineSteps(rootNode)
      maxSteps.value = history.value.length
      currentStep.value = 0
      renderTree(history.value[currentStep.value])
    }
  }
})
</script>

<template>
  <div class="deductive-tree-graph relative w-full h-full">
    <svg v-if="history.length > 0 && history[currentStep]" ref="svgRef" class="w-full h-full"></svg>

    <!-- 时间线控制面板 -->
    <div v-if="history.length > 0" class="absolute bottom-4 left-1/2 -translate-x-1/2 bg-white dark:bg-slate-800 rounded-lg shadow-lg p-4 border border-slate-200 dark:border-slate-700">
      <div class="flex items-center space-x-4">
        <!-- 导航按钮 -->
        <button
          @click="goBack"
          :disabled="!canGoBack"
          class="px-3 py-2 bg-blue-500 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors"
          title="上一步"
        >
          ←
        </button>

        <!-- 步骤指示器 -->
        <div class="flex items-center space-x-2">
          <span class="text-sm text-slate-600 dark:text-slate-400">
            {{ currentStep + 1 }} / {{ maxSteps }}
          </span>
          <div class="flex space-x-1">
            <div
              v-for="(_, index) in history"
              :key="index"
              :class="[
                'w-2 h-2 rounded-full transition-colors',
                index === currentStep ? 'bg-blue-500' : 'bg-slate-300 dark:bg-slate-600'
              ]"
            ></div>
          </div>
        </div>

        <button
          @click="goForward"
          :disabled="!canGoForward"
          class="px-3 py-2 bg-blue-500 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors"
          title="下一步"
        >
          →
        </button>

        <!-- 自动播放按钮 -->
        <button
          @click="autoPlay ? stopAutoPlay() : startAutoPlay()"
          :disabled="currentStep >= maxSteps - 1"
          class="px-3 py-2 bg-green-500 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-green-600 transition-colors"
          :title="autoPlay ? '停止自动播放' : '开始自动播放'"
        >
          {{ autoPlay ? '⏸' : '▶' }}
        </button>
      </div>

      <!-- 步骤描述 -->
      <div class="mt-2 text-xs text-slate-500 dark:text-slate-400 text-center">
        <span v-if="currentStep === 0">初始状态：显示根节点</span>
        <span v-else-if="currentStep === 1">第一层展开：显示主要分支</span>
        <span v-else>完整推演：显示所有节点</span>
      </div>
    </div>

    <div v-else class="flex items-center justify-center w-full h-full text-slate-500 dark:text-slate-400">
      <p>等待推演数据...</p>
    </div>
  </div>
</template>

<style scoped>
/* Add any specific styles for the tree graph here */
</style>

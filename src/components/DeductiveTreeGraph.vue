<script setup lang="ts">
import { ref, watch, onMounted, computed, nextTick } from 'vue'
import * as d3 from 'd3'
import { AnalysisNode, NodeConnection } from '../utils/systemicThinking'
import { LLMDeductiveAnalyzer } from '../utils/deductiveThinking'

interface NetworkNode extends AnalysisNode {
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
  vx?: number;
  vy?: number;
  index?: number;
}

interface NetworkLink extends NodeConnection {
  source: NetworkNode | string | number;
  target: NetworkNode | string | number;
  index?: number;
}

interface Props {
  nodes: AnalysisNode[];
  connections: NodeConnection[];
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'nodes-updated': [nodes: AnalysisNode[]]
  'connections-updated': [connections: NodeConnection[]]
}>()

const svgRef = ref<SVGElement | null>(null)
const currentStep = ref(0)
const history = ref<{ nodes: NetworkNode[], links: NetworkLink[] }[]>([])
const maxSteps = ref(0)
const editingNode = ref<string | null>(null)
const editingText = ref('')
const hoveredNode = ref<string | null>(null)
const showNodeActions = ref(false)
const nodeActionPosition = ref({ x: 0, y: 0 })

const width = 1200
const height = 800

// 拖拽事件处理
const dragstarted = (event: any, d: NetworkNode) => {
  if (!event.active) event.subject.fx = event.subject.x, event.subject.fy = event.subject.y
}

const dragged = (event: any, d: NetworkNode) => {
  event.subject.fx = event.x
  event.subject.fy = event.y
}

const dragended = (event: any, d: NetworkNode) => {
  if (!event.active) event.subject.fx = null, event.subject.fy = null
}

// 节点交互事件处理
const handleNodeMouseOver = (event: MouseEvent, d: NetworkNode) => {
  hoveredNode.value = d.id
  const rect = (event.target as Element).getBoundingClientRect()
  nodeActionPosition.value = { x: rect.left, y: rect.top }
}

const handleNodeMouseOut = (event: MouseEvent, d: NetworkNode) => {
  setTimeout(() => {
    if (hoveredNode.value === d.id) {
      hoveredNode.value = null
      showNodeActions.value = false
    }
  }, 100)
}

const handleNodeDoubleClick = (event: MouseEvent, d: NetworkNode) => {
  event.stopPropagation()
  editingNode.value = d.id
  editingText.value = d.title
}

const handleNodeClick = (event: MouseEvent, d: NetworkNode) => {
  if (hoveredNode.value === d.id) {
    showNodeActions.value = true
  }
}

// 神经网络样式的力导向图渲染
const renderNetwork = (networkData: { nodes: NetworkNode[], links: NetworkLink[] }) => {
  console.log('DeductiveTreeGraph: Rendering network with data:', networkData);
  if (!svgRef.value || !networkData.nodes.length) {
    console.warn('DeductiveTreeGraph: Cannot render - missing svgRef or nodes');
    return
  }

  try {
    d3.select(svgRef.value).selectAll('*').remove()

    const svg = d3.select(svgRef.value)
      .attr("width", width)
      .attr("height", height)

    // 创建力导向仿真
    const simulation = d3.forceSimulation(networkData.nodes)
      .force("link", d3.forceLink(networkData.links).id((d: any) => d.id).distance(100).strength(0.5))
      .force("charge", d3.forceManyBody().strength(-300))
      .force("center", d3.forceCenter(width / 2, height / 2))
      .force("collision", d3.forceCollide().radius(30))

    // 创建连接线
    const links = svg.append("g")
      .attr("class", "links")
      .selectAll("line")
      .data(networkData.links)
      .enter().append("line")
      .attr("stroke", d => {
        const sourceType = (d.source as NetworkNode).type
        const targetType = (d.target as NetworkNode).type
        if (sourceType === 'positive' || targetType === 'positive') return '#22c55e'
        if (sourceType === 'negative' || targetType === 'negative') return '#ef4444'
        return '#94a3b8'
      })
      .attr("stroke-width", d => Math.max(1, d.strength * 3))
      .attr("stroke-opacity", 0.6)

    // 创建节点组
    const nodeGroups = svg.append("g")
      .attr("class", "nodes")
      .selectAll("g")
      .data(networkData.nodes)
      .enter().append("g")
      .attr("class", "node-group")
      .call(d3.drag<any, NetworkNode>()
        .on("start", dragstarted)
        .on("drag", dragged)
        .on("end", dragended))

    // 节点圆圈
    const nodeCircles = nodeGroups.append("circle")
      .attr("r", d => {
        if (d.type === 'initial') return 20
        if (d.impact === 'critical') return 16
        if (d.impact === 'high') return 14
        return 12
      })
      .attr("fill", d => {
        if (d.type === 'initial') return '#6366f1'
        if (d.type === 'positive') return '#22c55e'
        if (d.type === 'negative') return '#ef4444'
        return '#64748b'
      })
      .attr("stroke", "#fff")
      .attr("stroke-width", 2)
      .style("cursor", "pointer")

    // 节点文本
    const nodeTexts = nodeGroups.append("text")
      .attr("dy", ".35em")
      .attr("text-anchor", "middle")
      .style("font-size", "10px")
      .style("font-weight", "bold")
      .style("fill", "#fff")
      .style("pointer-events", "none")
      .text(d => d.title.length > 8 ? d.title.substring(0, 8) + '...' : d.title)

    // 节点标签（在节点下方）
    const nodeLabels = nodeGroups.append("text")
      .attr("dy", "2.5em")
      .attr("text-anchor", "middle")
      .style("font-size", "9px")
      .style("fill", "#374151")
      .style("pointer-events", "none")
      .text(d => d.title)

    // 添加节点交互事件
    nodeGroups
      .on("mouseover", handleNodeMouseOver)
      .on("mouseout", handleNodeMouseOut)
      .on("dblclick", handleNodeDoubleClick)
      .on("click", handleNodeClick)

    // 仿真更新函数
    simulation.on("tick", () => {
      links
        .attr("x1", (d: any) => d.source.x)
        .attr("y1", (d: any) => d.source.y)
        .attr("x2", (d: any) => d.target.x)
        .attr("y2", (d: any) => d.target.y)

      nodeGroups
        .attr("transform", d => `translate(${d.x},${d.y})`)
    })

    console.log('DeductiveTreeGraph: Network rendered with', networkData.nodes.length, 'nodes and', networkData.links.length, 'links');
  } catch (error) {
    console.error('DeductiveTreeGraph: Error rendering network:', error);
  }
}

// 将数据转换为网络格式
const buildNetwork = (nodes: AnalysisNode[], connections: NodeConnection[]): { nodes: NetworkNode[], links: NetworkLink[] } => {
  console.log('DeductiveTreeGraph: Building network with nodes:', nodes, 'and connections:', connections);

  // 转换节点
  const networkNodes: NetworkNode[] = nodes.map(node => ({
    ...node,
    x: Math.random() * width,
    y: Math.random() * height
  }))

  // 转换连接
  const networkLinks: NetworkLink[] = connections.map(conn => ({
    ...conn,
    source: conn.from,
    target: conn.to
  }))

  return { nodes: networkNodes, links: networkLinks }
}

// 生成更多节点的函数
const generateAdditionalNodes = async (baseNode: NetworkNode, direction: 'positive' | 'negative'): Promise<{ nodes: NetworkNode[], links: NetworkLink[] }> => {
  const newNodes: NetworkNode[] = []
  const newLinks: NetworkLink[] = []

  // 生成3-5个新节点
  const nodeCount = Math.floor(Math.random() * 3) + 3

  for (let i = 0; i < nodeCount; i++) {
    const nodeId = `${direction}-${baseNode.id}-${Date.now()}-${i}`
    const newNode: NetworkNode = {
      id: nodeId,
      title: `${direction === 'positive' ? '积极' : '负面'}推演 ${i + 1}`,
      description: `基于"${baseNode.title}"的${direction === 'positive' ? '积极' : '负面'}方向推演`,
      level: baseNode.level + 1,
      impact: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
      modelSource: `deductive-${direction}`,
      type: direction,
      x: baseNode.x! + (Math.random() - 0.5) * 200,
      y: baseNode.y! + (Math.random() - 0.5) * 200
    }

    newNodes.push(newNode)

    // 创建连接
    newLinks.push({
      from: baseNode.id,
      to: nodeId,
      type: 'strong',
      description: `${direction}推演连接`,
      strength: Math.random() * 0.5 + 0.5,
      source: baseNode.id,
      target: nodeId
    })

    // 有概率在新节点之间创建连接
    if (i > 0 && Math.random() > 0.5) {
      const prevNodeId = `${direction}-${baseNode.id}-${Date.now()}-${i-1}`
      newLinks.push({
        from: prevNodeId,
        to: nodeId,
        type: 'weak',
        description: '关联连接',
        strength: Math.random() * 0.3 + 0.2,
        source: prevNodeId,
        target: nodeId
      })
    }
  }

  return { nodes: newNodes, links: newLinks }
}

// 创建时间线步骤 - 网络版本
const createNetworkTimelineSteps = (networkData: { nodes: NetworkNode[], links: NetworkLink[] }): { nodes: NetworkNode[], links: NetworkLink[] }[] => {
  const steps: { nodes: NetworkNode[], links: NetworkLink[] }[] = []
  const { nodes, links } = networkData

  // 步骤1: 只显示初始节点
  const initialNodes = nodes.filter(n => n.type === 'initial')
  steps.push({ nodes: initialNodes, links: [] })

  // 步骤2: 显示初始节点和第一层节点
  const firstLevelNodes = nodes.filter(n => n.level <= 1)
  const firstLevelLinks = links.filter(l => {
    const sourceNode = nodes.find(n => n.id === l.from)
    const targetNode = nodes.find(n => n.id === l.to)
    return sourceNode && targetNode && sourceNode.level <= 1 && targetNode.level <= 1
  })
  steps.push({ nodes: firstLevelNodes, links: firstLevelLinks })

  // 步骤3: 显示所有节点
  steps.push({ nodes, links })

  return steps
}

watch([() => props.nodes, () => props.connections], ([newNodes, newConnections]) => {
  if (newNodes.length > 0) {
    const networkData = buildNetwork(newNodes, newConnections)
    if (networkData.nodes.length > 0) {
      history.value = createNetworkTimelineSteps(networkData)
      maxSteps.value = history.value.length
      currentStep.value = 0
      renderNetwork(history.value[currentStep.value])
    }
  }
}, { immediate: true, deep: true })

const canGoBack = computed(() => currentStep.value > 0)
const canGoForward = computed(() => currentStep.value < history.value.length - 1)

const navigateHistory = (step: number) => {
  if (step >= 0 && step < history.value.length) {
    currentStep.value = step
    const networkData = history.value[currentStep.value]
    if (networkData) {
      renderNetwork(networkData)
    }
  }
}

const goBack = () => {
  if (canGoBack.value) {
    navigateHistory(currentStep.value - 1)
  }
}

const goForward = () => {
  if (canGoForward.value) {
    navigateHistory(currentStep.value + 1)
  }
}

// 自动播放功能
const autoPlay = ref(false)
const autoPlayInterval = ref<number | null>(null)

const startAutoPlay = () => {
  if (autoPlayInterval.value) return

  autoPlay.value = true
  autoPlayInterval.value = window.setInterval(() => {
    if (canGoForward.value) {
      goForward()
    } else {
      stopAutoPlay()
    }
  }, 2000)
}

const stopAutoPlay = () => {
  if (autoPlayInterval.value) {
    clearInterval(autoPlayInterval.value)
    autoPlayInterval.value = null
  }
  autoPlay.value = false
}

// 节点编辑功能
const saveNodeEdit = () => {
  if (editingNode.value && editingText.value.trim()) {
    // 更新当前步骤中的节点
    const currentData = history.value[currentStep.value]
    const nodeToUpdate = currentData.nodes.find(n => n.id === editingNode.value)
    if (nodeToUpdate) {
      nodeToUpdate.title = editingText.value.trim()
      // 重新渲染
      renderNetwork(currentData)
      // 发出更新事件
      emit('nodes-updated', currentData.nodes)
    }
  }
  editingNode.value = null
  editingText.value = ''
}

const cancelNodeEdit = () => {
  editingNode.value = null
  editingText.value = ''
}

// 动态生成节点
const generatePositiveNodes = async (baseNode: NetworkNode) => {
  const newData = await generateAdditionalNodes(baseNode, 'positive')
  const currentData = history.value[currentStep.value]

  // 添加新节点和连接
  currentData.nodes.push(...newData.nodes)
  currentData.links.push(...newData.links)

  // 重新渲染
  renderNetwork(currentData)
  emit('nodes-updated', currentData.nodes)
  emit('connections-updated', currentData.links)

  showNodeActions.value = false
}

const generateNegativeNodes = async (baseNode: NetworkNode) => {
  const newData = await generateAdditionalNodes(baseNode, 'negative')
  const currentData = history.value[currentStep.value]

  // 添加新节点和连接
  currentData.nodes.push(...newData.nodes)
  currentData.links.push(...newData.links)

  // 重新渲染
  renderNetwork(currentData)
  emit('nodes-updated', currentData.nodes)
  emit('connections-updated', currentData.links)

  showNodeActions.value = false
}

onMounted(() => {
  // Initial render if data is already available
  if (props.nodes.length > 0) {
    const networkData = buildNetwork(props.nodes, props.connections)
    if (networkData.nodes.length > 0) {
      history.value = createNetworkTimelineSteps(networkData)
      maxSteps.value = history.value.length
      currentStep.value = 0
      renderNetwork(history.value[currentStep.value])
    }
  }
})
</script>

<template>
  <div class="deductive-tree-graph relative w-full h-full">
    <svg v-if="history.length > 0 && history[currentStep]" ref="svgRef" class="w-full h-full"></svg>

    <!-- 节点编辑弹窗 -->
    <div v-if="editingNode" class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-slate-800 rounded-lg p-6 shadow-xl max-w-md w-full mx-4">
        <h3 class="text-lg font-semibold mb-4 text-slate-800 dark:text-slate-200">编辑节点</h3>
        <input
          v-model="editingText"
          type="text"
          class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="输入节点标题"
          @keyup.enter="saveNodeEdit"
          @keyup.escape="cancelNodeEdit"
        />
        <div class="flex justify-end space-x-2 mt-4">
          <button
            @click="cancelNodeEdit"
            class="px-4 py-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200"
          >
            取消
          </button>
          <button
            @click="saveNodeEdit"
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            保存
          </button>
        </div>
      </div>
    </div>

    <!-- 节点操作菜单 -->
    <div
      v-if="showNodeActions && hoveredNode"
      class="absolute z-40 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700 p-2"
      :style="{ left: nodeActionPosition.x + 'px', top: nodeActionPosition.y + 'px' }"
    >
      <button
        @click="generatePositiveNodes(history[currentStep].nodes.find(n => n.id === hoveredNode)!)"
        class="block w-full text-left px-3 py-2 text-sm text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 rounded"
      >
        ➕ 生成积极方向
      </button>
      <button
        @click="generateNegativeNodes(history[currentStep].nodes.find(n => n.id === hoveredNode)!)"
        class="block w-full text-left px-3 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
      >
        ➖ 生成消极方向
      </button>
    </div>

    <!-- 时间线控制面板 -->
    <div v-if="history.length > 0" class="absolute bottom-4 left-1/2 -translate-x-1/2 bg-white dark:bg-slate-800 rounded-lg shadow-lg p-4 border border-slate-200 dark:border-slate-700">
      <div class="flex items-center space-x-4">
        <!-- 导航按钮 -->
        <button
          @click="goBack"
          :disabled="!canGoBack"
          class="px-3 py-2 bg-blue-500 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors"
          title="上一步"
        >
          ←
        </button>

        <!-- 步骤指示器 -->
        <div class="flex items-center space-x-2">
          <span class="text-sm text-slate-600 dark:text-slate-400">
            {{ currentStep + 1 }} / {{ maxSteps }}
          </span>
          <div class="flex space-x-1">
            <div
              v-for="(_, index) in history"
              :key="index"
              :class="[
                'w-2 h-2 rounded-full transition-colors',
                index === currentStep ? 'bg-blue-500' : 'bg-slate-300 dark:bg-slate-600'
              ]"
            ></div>
          </div>
        </div>

        <button
          @click="goForward"
          :disabled="!canGoForward"
          class="px-3 py-2 bg-blue-500 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors"
          title="下一步"
        >
          →
        </button>

        <!-- 自动播放按钮 -->
        <button
          @click="autoPlay ? stopAutoPlay() : startAutoPlay()"
          :disabled="currentStep >= maxSteps - 1"
          class="px-3 py-2 bg-green-500 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-green-600 transition-colors"
          :title="autoPlay ? '停止自动播放' : '开始自动播放'"
        >
          {{ autoPlay ? '⏸' : '▶' }}
        </button>
      </div>

      <!-- 步骤描述 -->
      <div class="mt-2 text-xs text-slate-500 dark:text-slate-400 text-center">
        <span v-if="currentStep === 0">初始状态：显示核心节点</span>
        <span v-else-if="currentStep === 1">第一层展开：显示主要分支</span>
        <span v-else>完整网络：显示所有推演节点</span>
      </div>

      <!-- 操作提示 -->
      <div class="mt-2 text-xs text-slate-400 dark:text-slate-500 text-center">
        双击节点编辑 • 悬停节点查看选项
      </div>
    </div>

    <div v-else class="flex items-center justify-center w-full h-full text-slate-500 dark:text-slate-400">
      <div class="text-center">
        <div class="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
        <p>正在构建推演网络...</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add any specific styles for the tree graph here */
</style>

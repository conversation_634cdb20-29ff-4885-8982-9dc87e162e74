<script setup lang="ts">
import { ref, watch, onMounted, computed, nextTick } from 'vue'
import * as d3 from 'd3'
import { AnalysisNode, NodeConnection } from '../utils/systemicThinking'
import { LLMDeductiveAnalyzer } from '../utils/deductiveThinking'

interface NetworkNode extends AnalysisNode {
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
  vx?: number;
  vy?: number;
  index?: number;
}

interface NetworkLink extends NodeConnection {
  source: NetworkNode | string | number;
  target: NetworkNode | string | number;
  index?: number;
}

interface Props {
  nodes: AnalysisNode[];
  connections: NodeConnection[];
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'nodes-updated': [nodes: AnalysisNode[]]
  'connections-updated': [connections: NodeConnection[]]
}>()

const svgRef = ref<SVGElement | null>(null)
const currentStep = ref(0)
const history = ref<{ nodes: NetworkNode[], links: NetworkLink[] }[]>([])
const maxSteps = ref(0)
const editingNode = ref<string | null>(null)
const editingText = ref('')
const hoveredNode = ref<string | null>(null)
const showNodeActions = ref(false)
const nodeActionPosition = ref({ x: 0, y: 0 })
const zoomBehavior = ref<any>(null)
const currentZoom = ref(1)

const width = 1200
const height = 800

// 拖拽事件处理
const dragstarted = (event: any, d: NetworkNode) => {
  if (!event.active) {
    const simulation = d3.select(svgRef.value).select('.simulation').datum()
    if (simulation) simulation.alphaTarget(0.3).restart()
  }
  d.fx = d.x
  d.fy = d.y
}

const dragged = (event: any, d: NetworkNode) => {
  d.fx = event.x
  d.fy = event.y
}

const dragended = (event: any, d: NetworkNode) => {
  if (!event.active) {
    const simulation = d3.select(svgRef.value).select('.simulation').datum()
    if (simulation) simulation.alphaTarget(0)
  }
  d.fx = null
  d.fy = null
}

// 节点交互事件处理
const handleNodeMouseOver = (event: any, d: NetworkNode) => {
  hoveredNode.value = d.id
  // 获取SVG相对位置
  const svgRect = svgRef.value!.getBoundingClientRect()
  const nodeRect = (event.target as Element).getBoundingClientRect()
  nodeActionPosition.value = {
    x: nodeRect.left - svgRect.left + nodeRect.width / 2,
    y: nodeRect.top - svgRect.top + nodeRect.height + 10
  }

  // 延迟显示菜单，避免快速移动时闪烁
  setTimeout(() => {
    if (hoveredNode.value === d.id) {
      showNodeActions.value = true
    }
  }, 300)
}

const handleNodeMouseOut = (event: any, d: NetworkNode) => {
  // 检查鼠标是否移动到菜单上
  const relatedTarget = event.relatedTarget
  if (relatedTarget && relatedTarget.closest('.node-actions-menu')) {
    return // 不隐藏菜单
  }

  setTimeout(() => {
    if (hoveredNode.value === d.id && !showNodeActions.value) {
      hoveredNode.value = null
    }
  }, 200)
}

const handleNodeDoubleClick = (event: any, d: NetworkNode) => {
  event.stopPropagation()
  showNodeActions.value = false
  editingNode.value = d.id
  editingText.value = d.title
}

const handleNodeClick = (event: any, d: NetworkNode) => {
  event.stopPropagation()
  if (hoveredNode.value === d.id && !showNodeActions.value) {
    showNodeActions.value = true
  }
}

// 菜单交互处理
const handleMenuMouseEnter = () => {
  // 保持菜单显示
}

const handleMenuMouseLeave = () => {
  showNodeActions.value = false
  hoveredNode.value = null
}

// 缩放控制函数
const zoomIn = () => {
  if (svgRef.value && zoomBehavior.value) {
    d3.select(svgRef.value)
      .transition()
      .duration(300)
      .call(zoomBehavior.value.scaleBy, 1.5)
  }
}

const zoomOut = () => {
  if (svgRef.value && zoomBehavior.value) {
    d3.select(svgRef.value)
      .transition()
      .duration(300)
      .call(zoomBehavior.value.scaleBy, 1 / 1.5)
  }
}

const resetZoom = () => {
  if (svgRef.value && zoomBehavior.value) {
    d3.select(svgRef.value)
      .transition()
      .duration(500)
      .call(zoomBehavior.value.transform, d3.zoomIdentity)
  }
}

// 神经网络样式的力导向图渲染
const renderNetwork = (networkData: { nodes: NetworkNode[], links: NetworkLink[] }) => {
  console.log('DeductiveTreeGraph: Rendering network with data:', networkData);
  if (!svgRef.value || !networkData.nodes.length) {
    console.warn('DeductiveTreeGraph: Cannot render - missing svgRef or nodes');
    return
  }

  try {
    d3.select(svgRef.value).selectAll('*').remove()

    const svg = d3.select(svgRef.value)
      .attr("width", width)
      .attr("height", height)

    // 添加缩放和平移功能
    const zoom = d3.zoom()
      .scaleExtent([0.1, 4])
      .on("zoom", (event: any) => {
        container.attr("transform", event.transform)
        currentZoom.value = event.transform.k
      })

    svg.call(zoom as any)
    zoomBehavior.value = zoom

    // 创建容器组
    const container = svg.append("g")
      .attr("class", "zoom-container")

    // 创建力导向仿真
    const simulation = d3.forceSimulation(networkData.nodes)
      .force("link", d3.forceLink(networkData.links).id((d: any) => d.id).distance(100).strength(0.5))
      .force("charge", d3.forceManyBody().strength(-300))
      .force("center", d3.forceCenter(width / 2, height / 2))
      .force("collision", d3.forceCollide().radius(30))

    // 创建连接线
    const links = container.append("g")
      .attr("class", "links")
      .selectAll("line")
      .data(networkData.links)
      .enter().append("line")
      .attr("stroke", (d: any) => {
        const sourceType = (d.source as NetworkNode).type
        const targetType = (d.target as NetworkNode).type
        if (sourceType === 'positive' || targetType === 'positive') return '#22c55e'
        if (sourceType === 'negative' || targetType === 'negative') return '#ef4444'
        return '#94a3b8'
      })
      .attr("stroke-width", (d: any) => Math.max(1, d.strength * 3))
      .attr("stroke-opacity", 0.6)

    // 创建节点组
    const nodeGroups = container.append("g")
      .attr("class", "nodes")
      .selectAll("g")
      .data(networkData.nodes)
      .enter().append("g")
      .attr("class", "node-group")
      .style("cursor", "grab")
      .call(d3.drag<any, NetworkNode>()
        .on("start", (event, d) => {
          if (!event.active) simulation.alphaTarget(0.3).restart()
          d.fx = d.x
          d.fy = d.y
          d3.select(event.currentTarget).style("cursor", "grabbing")
        })
        .on("drag", (event, d) => {
          d.fx = event.x
          d.fy = event.y
        })
        .on("end", (event, d) => {
          if (!event.active) simulation.alphaTarget(0)
          d.fx = null
          d.fy = null
          d3.select(event.currentTarget).style("cursor", "grab")
        }))

    // 节点圆圈
    nodeGroups.append("circle")
      .attr("r", (d: any) => {
        if (d.type === 'initial') return 18
        if (d.impact === 'critical') return 15
        if (d.impact === 'high') return 13
        return 11
      })
      .attr("fill", (d: any) => {
        if (d.type === 'initial') return '#6366f1'
        if (d.type === 'positive') return '#10b981'
        if (d.type === 'negative') return '#ef4444'
        return '#64748b'
      })
      .attr("stroke", "#fff")
      .attr("stroke-width", 1.5)
      .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))")

    // 节点文本（只在节点内显示简短文本）
    nodeGroups.append("text")
      .attr("dy", ".35em")
      .attr("text-anchor", "middle")
      .style("font-size", "9px")
      .style("font-weight", "600")
      .style("fill", "#fff")
      .style("pointer-events", "none")
      .text((d: any) => {
        if (d.type === 'initial') return '核心'
        if (d.type === 'positive') return '+'
        if (d.type === 'negative') return '−'
        return '•'
      })

    // 节点标签（悬停时显示完整标题）
    nodeGroups.append("title")
      .text((d: any) => d.title)

    // 添加节点交互事件
    nodeGroups
      .on("mouseover", handleNodeMouseOver)
      .on("mouseout", handleNodeMouseOut)
      .on("dblclick", handleNodeDoubleClick)
      .on("click", handleNodeClick)

    // 仿真更新函数
    simulation.on("tick", () => {
      links
        .attr("x1", (d: any) => d.source.x)
        .attr("y1", (d: any) => d.source.y)
        .attr("x2", (d: any) => d.target.x)
        .attr("y2", (d: any) => d.target.y)

      nodeGroups
        .attr("transform", (d: any) => `translate(${d.x},${d.y})`)
    })

    console.log('DeductiveTreeGraph: Network rendered with', networkData.nodes.length, 'nodes and', networkData.links.length, 'links');
  } catch (error) {
    console.error('DeductiveTreeGraph: Error rendering network:', error);
  }
}

// 将数据转换为网络格式
const buildNetwork = (nodes: AnalysisNode[], connections: NodeConnection[]): { nodes: NetworkNode[], links: NetworkLink[] } => {
  console.log('DeductiveTreeGraph: Building network with nodes:', nodes, 'and connections:', connections);

  // 转换节点
  const networkNodes: NetworkNode[] = nodes.map(node => ({
    ...node,
    x: Math.random() * width,
    y: Math.random() * height
  }))

  // 转换连接
  const networkLinks: NetworkLink[] = connections.map(conn => ({
    ...conn,
    source: conn.from,
    target: conn.to
  }))

  return { nodes: networkNodes, links: networkLinks }
}

// 使用LLM生成更多节点的函数
const generateAdditionalNodes = async (baseNode: NetworkNode, direction: 'positive' | 'negative'): Promise<{ nodes: NetworkNode[], links: NetworkLink[] }> => {
  try {
    // 获取当前所有节点的上下文
    const currentData = history.value[currentStep.value]
    const contextNodes = currentData.nodes.map(n => `${n.title}: ${n.description}`).join('\n')

    // 构建LLM提示
    const prompt = `基于现有的推演网络，为节点"${baseNode.title}"生成${direction === 'positive' ? '积极' : '负面'}方向的扩展推演。

当前推演网络包含以下节点：
${contextNodes}

请为"${baseNode.title}"生成3个${direction === 'positive' ? '积极' : '负面'}方向的子节点，要求：
1. 与现有节点相关但不重复
2. 具有逻辑性和深度
3. 符合${direction === 'positive' ? '积极' : '负面'}推演的方向

请严格按照以下JSON格式返回：
{
  "nodes": [
    {
      "title": "节点标题",
      "description": "节点描述",
      "impact": "low|medium|high|critical"
    }
  ]
}

只返回JSON，不要其他内容。`

    // 调用LLM API
    const analyzer = new (await import('../utils/deductiveThinking')).LLMDeductiveAnalyzer()
    const response = await analyzer.callLLMForExpansion(prompt)

    // 解析响应
    const parsedResponse = JSON.parse(response.text)
    const newNodes: NetworkNode[] = []
    const newLinks: NetworkLink[] = []

    // 创建新节点
    parsedResponse.nodes.forEach((nodeData: any, index: number) => {
      const nodeId = `${direction}-${baseNode.id}-${Date.now()}-${index}`
      const newNode: NetworkNode = {
        id: nodeId,
        title: nodeData.title,
        description: nodeData.description,
        level: baseNode.level + 1,
        impact: nodeData.impact || 'medium',
        modelSource: `deductive-${direction}`,
        type: direction,
        x: baseNode.x! + (Math.random() - 0.5) * 200,
        y: baseNode.y! + (Math.random() - 0.5) * 200
      }

      newNodes.push(newNode)

      // 创建连接
      newLinks.push({
        from: baseNode.id,
        to: nodeId,
        type: 'strong',
        description: `${direction}推演连接`,
        strength: 0.8,
        source: baseNode.id,
        target: nodeId
      })
    })

    return { nodes: newNodes, links: newLinks }

  } catch (error) {
    console.error('LLM生成失败，使用本地生成:', error)

    // 回退到简单的本地生成
    const newNodes: NetworkNode[] = []
    const newLinks: NetworkLink[] = []

    for (let i = 0; i < 2; i++) {
      const nodeId = `${direction}-${baseNode.id}-${Date.now()}-${i}`
      const newNode: NetworkNode = {
        id: nodeId,
        title: `${baseNode.title}的${direction === 'positive' ? '积极' : '负面'}影响${i + 1}`,
        description: `基于"${baseNode.title}"的${direction === 'positive' ? '积极' : '负面'}方向推演`,
        level: baseNode.level + 1,
        impact: 'medium',
        modelSource: `deductive-${direction}`,
        type: direction,
        x: baseNode.x! + (Math.random() - 0.5) * 200,
        y: baseNode.y! + (Math.random() - 0.5) * 200
      }

      newNodes.push(newNode)

      newLinks.push({
        from: baseNode.id,
        to: nodeId,
        type: 'strong',
        description: `${direction}推演连接`,
        strength: 0.7,
        source: baseNode.id,
        target: nodeId
      })
    }

    return { nodes: newNodes, links: newLinks }
  }
}

// 创建时间线步骤 - 网络版本
const createNetworkTimelineSteps = (networkData: { nodes: NetworkNode[], links: NetworkLink[] }): { nodes: NetworkNode[], links: NetworkLink[] }[] => {
  const steps: { nodes: NetworkNode[], links: NetworkLink[] }[] = []
  const { nodes, links } = networkData

  // 步骤1: 只显示初始节点
  const initialNodes = nodes.filter(n => n.type === 'initial')
  steps.push({ nodes: initialNodes, links: [] })

  // 步骤2: 显示初始节点和第一层节点
  const firstLevelNodes = nodes.filter(n => n.level <= 1)
  const firstLevelLinks = links.filter(l => {
    const sourceNode = nodes.find(n => n.id === l.from)
    const targetNode = nodes.find(n => n.id === l.to)
    return sourceNode && targetNode && sourceNode.level <= 1 && targetNode.level <= 1
  })
  steps.push({ nodes: firstLevelNodes, links: firstLevelLinks })

  // 步骤3: 显示所有节点
  steps.push({ nodes, links })

  return steps
}

watch([() => props.nodes, () => props.connections], ([newNodes, newConnections]) => {
  if (newNodes.length > 0) {
    const networkData = buildNetwork(newNodes, newConnections)
    if (networkData.nodes.length > 0) {
      history.value = createNetworkTimelineSteps(networkData)
      maxSteps.value = history.value.length
      currentStep.value = 0
      renderNetwork(history.value[currentStep.value])
    }
  }
}, { immediate: true, deep: true })

const canGoBack = computed(() => currentStep.value > 0)
const canGoForward = computed(() => currentStep.value < history.value.length - 1)

const navigateHistory = (step: number) => {
  if (step >= 0 && step < history.value.length) {
    currentStep.value = step
    const networkData = history.value[currentStep.value]
    if (networkData) {
      renderNetwork(networkData)
    }
  }
}

const goBack = () => {
  if (canGoBack.value) {
    navigateHistory(currentStep.value - 1)
  }
}

const goForward = () => {
  if (canGoForward.value) {
    navigateHistory(currentStep.value + 1)
  }
}

// 移除自动播放功能以简化界面

// 节点编辑功能
const saveNodeEdit = () => {
  if (editingNode.value && editingText.value.trim()) {
    // 更新当前步骤中的节点
    const currentData = history.value[currentStep.value]
    const nodeToUpdate = currentData.nodes.find(n => n.id === editingNode.value)
    if (nodeToUpdate) {
      nodeToUpdate.title = editingText.value.trim()
      // 重新渲染
      renderNetwork(currentData)
      // 发出更新事件
      emit('nodes-updated', currentData.nodes)
    }
  }
  editingNode.value = null
  editingText.value = ''
}

const cancelNodeEdit = () => {
  editingNode.value = null
  editingText.value = ''
}

// 动态生成节点
const generatePositiveNodes = async (baseNode: NetworkNode) => {
  const newData = await generateAdditionalNodes(baseNode, 'positive')
  const currentData = history.value[currentStep.value]

  // 添加新节点和连接
  currentData.nodes.push(...newData.nodes)
  currentData.links.push(...newData.links)

  // 重新渲染
  renderNetwork(currentData)
  emit('nodes-updated', currentData.nodes)
  emit('connections-updated', currentData.links)

  showNodeActions.value = false
}

const generateNegativeNodes = async (baseNode: NetworkNode) => {
  const newData = await generateAdditionalNodes(baseNode, 'negative')
  const currentData = history.value[currentStep.value]

  // 添加新节点和连接
  currentData.nodes.push(...newData.nodes)
  currentData.links.push(...newData.links)

  // 重新渲染
  renderNetwork(currentData)
  emit('nodes-updated', currentData.nodes)
  emit('connections-updated', currentData.links)

  showNodeActions.value = false
}

onMounted(() => {
  // Initial render if data is already available
  if (props.nodes.length > 0) {
    const networkData = buildNetwork(props.nodes, props.connections)
    if (networkData.nodes.length > 0) {
      history.value = createNetworkTimelineSteps(networkData)
      maxSteps.value = history.value.length
      currentStep.value = 0
      renderNetwork(history.value[currentStep.value])
    }
  }
})
</script>

<template>
  <div class="deductive-tree-graph relative w-full h-full">
    <svg v-if="history.length > 0 && history[currentStep]" ref="svgRef" class="w-full h-full"></svg>

    <!-- 简化的节点编辑弹窗 -->
    <div v-if="editingNode" class="absolute inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50">
      <div class="bg-white dark:bg-slate-800 rounded-lg p-4 shadow-xl max-w-sm w-full mx-4">
        <input
          v-model="editingText"
          type="text"
          class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          placeholder="输入节点标题"
          @keyup.enter="saveNodeEdit"
          @keyup.escape="cancelNodeEdit"
          autofocus
        />
        <div class="flex justify-end space-x-2 mt-3">
          <button
            @click="cancelNodeEdit"
            class="px-3 py-1 text-xs text-slate-600 hover:text-slate-800 rounded"
          >
            取消
          </button>
          <button
            @click="saveNodeEdit"
            class="px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
          >
            保存
          </button>
        </div>
      </div>
    </div>

    <!-- 简化的节点操作菜单 -->
    <div
      v-if="showNodeActions && hoveredNode"
      class="node-actions-menu absolute z-50 bg-white dark:bg-slate-800 rounded-md shadow-lg border border-slate-200 dark:border-slate-700 p-1"
      :style="{ left: nodeActionPosition.x + 'px', top: nodeActionPosition.y + 'px' }"
      @mouseenter="handleMenuMouseEnter"
      @mouseleave="handleMenuMouseLeave"
    >
      <button
        @click="generatePositiveNodes(history[currentStep].nodes.find(n => n.id === hoveredNode)!)"
        class="block w-full text-left px-2 py-1 text-xs text-green-600 hover:bg-green-50 rounded"
        title="生成积极方向"
      >
        ➕ 积极
      </button>
      <button
        @click="generateNegativeNodes(history[currentStep].nodes.find(n => n.id === hoveredNode)!)"
        class="block w-full text-left px-2 py-1 text-xs text-red-600 hover:bg-red-50 rounded"
        title="生成消极方向"
      >
        ➖ 消极
      </button>
      <button
        @click="handleNodeDoubleClick($event, history[currentStep].nodes.find(n => n.id === hoveredNode)!)"
        class="block w-full text-left px-2 py-1 text-xs text-blue-600 hover:bg-blue-50 rounded"
        title="编辑节点"
      >
        ✏️ 编辑
      </button>
    </div>

    <!-- 简化的控制面板 -->
    <div v-if="history.length > 0" class="absolute bottom-4 left-1/2 -translate-x-1/2 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm rounded-lg shadow-lg p-3 border border-slate-200/50 dark:border-slate-700/50">
      <div class="flex items-center space-x-3">
        <!-- 时间线导航 -->
        <div class="flex items-center space-x-2">
          <button
            @click="goBack"
            :disabled="!canGoBack"
            class="w-8 h-8 bg-blue-500 text-white rounded-full disabled:opacity-30 hover:bg-blue-600 transition-colors flex items-center justify-center text-sm"
            title="上一步"
          >
            ←
          </button>

          <div class="flex space-x-1">
            <div
              v-for="(_, index) in history"
              :key="index"
              :class="[
                'w-2 h-2 rounded-full transition-colors cursor-pointer',
                index === currentStep ? 'bg-blue-500' : 'bg-slate-300 dark:bg-slate-600'
              ]"
              @click="navigateHistory(index)"
            ></div>
          </div>

          <button
            @click="goForward"
            :disabled="!canGoForward"
            class="w-8 h-8 bg-blue-500 text-white rounded-full disabled:opacity-30 hover:bg-blue-600 transition-colors flex items-center justify-center text-sm"
            title="下一步"
          >
            →
          </button>
        </div>

        <!-- 分隔线 -->
        <div class="h-6 w-px bg-slate-300 dark:bg-slate-600"></div>

        <!-- 缩放控制 -->
        <div class="flex items-center space-x-1">
          <button
            @click="zoomOut"
            class="w-7 h-7 bg-slate-500 text-white rounded text-xs hover:bg-slate-600 transition-colors flex items-center justify-center"
            title="缩小"
          >
            −
          </button>
          <button
            @click="resetZoom"
            class="w-7 h-7 bg-slate-500 text-white rounded text-xs hover:bg-slate-600 transition-colors flex items-center justify-center"
            title="重置"
          >
            ⌂
          </button>
          <button
            @click="zoomIn"
            class="w-7 h-7 bg-slate-500 text-white rounded text-xs hover:bg-slate-600 transition-colors flex items-center justify-center"
            title="放大"
          >
            +
          </button>
        </div>
      </div>
    </div>

    <div v-else class="flex items-center justify-center w-full h-full text-slate-400">
      <div class="text-center">
        <div class="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
        <p class="text-sm">构建推演网络中...</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add any specific styles for the tree graph here */
</style>

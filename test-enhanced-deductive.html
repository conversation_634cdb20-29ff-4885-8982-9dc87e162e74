<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版推演分析测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 25px;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            transition: all 0.3s ease;
        }
        .test-section:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.1);
        }
        .test-button {
            display: inline-block;
            padding: 12px 24px;
            margin: 15px 15px 15px 0;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
        .test-button.secondary {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }
        .test-button.secondary:hover {
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }
        .description {
            color: #475569;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #3b82f6;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .feature-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
        }
        .feature-desc {
            font-size: 13px;
            color: #64748b;
        }
        h1 {
            text-align: center;
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 2.5em;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #64748b;
            margin-bottom: 40px;
            font-size: 1.1em;
        }
        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 ThinkFlash 增强版推演分析</h1>
        <p class="subtitle">神经网络样式 • 交互式节点编辑 • 动态生成</p>
        
        <div class="test-section">
            <h2><span class="emoji">🚀</span>核心功能测试</h2>
            <div class="description">测试基础的神经网络样式推演分析功能</div>
            <a href="http://localhost:5173/deductive-analysis?topic=人工智能在医疗诊断中的应用" 
               class="test-button" target="_blank">
                基础神经网络推演
            </a>
            <a href="http://localhost:5173/deductive-analysis?topic=可持续能源的发展前景&thinkingPrompt=从技术可行性、经济效益、环境影响三个角度分析" 
               class="test-button secondary" target="_blank">
                复杂主题推演
            </a>
        </div>
        
        <div class="test-section">
            <h2><span class="emoji">✨</span>新功能特性</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-title">🎯 神经网络布局</div>
                    <div class="feature-desc">力导向图算法，节点自动排列，支持拖拽调整</div>
                </div>
                <div class="feature-card">
                    <div class="feature-title">✏️ 节点编辑</div>
                    <div class="feature-desc">双击任意节点即可编辑标题，实时保存修改</div>
                </div>
                <div class="feature-card">
                    <div class="feature-title">🔄 动态生成</div>
                    <div class="feature-desc">悬停节点显示菜单，可继续生成积极/消极方向</div>
                </div>
                <div class="feature-card">
                    <div class="feature-title">📊 丰富节点</div>
                    <div class="feature-desc">16个节点，3层结构，复杂连接网络</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><span class="emoji">🎮</span>交互测试场景</h2>
            <div class="description">
                <strong>测试步骤：</strong><br>
                1. 点击下方按钮打开推演分析<br>
                2. 等待神经网络加载完成<br>
                3. 尝试拖拽节点重新排列<br>
                4. 双击任意节点进行编辑<br>
                5. 悬停节点查看操作菜单<br>
                6. 点击"生成积极/消极方向"添加新节点<br>
                7. 使用时间线控制面板导航
            </div>
            <a href="http://localhost:5173/deductive-analysis?topic=虚拟现实技术在教育中的革新&thinkingPrompt=考虑学习体验、技术门槛、成本效益、教师培训等多个维度" 
               class="test-button" target="_blank">
                开始交互测试
            </a>
        </div>
        
        <div class="test-section">
            <h2><span class="emoji">🔍</span>技术验证</h2>
            <div class="description">
                <strong>预期效果：</strong>
                <ul>
                    <li>🎨 节点颜色：蓝色(初始) • 绿色(积极) • 红色(负面)</li>
                    <li>📏 节点大小：根据影响程度自动调整</li>
                    <li>🔗 连接线：颜色和粗细反映关系强度</li>
                    <li>⚡ 动画效果：平滑的力导向布局动画</li>
                    <li>📱 响应式：支持不同屏幕尺寸</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h2><span class="emoji">🛠️</span>调试信息</h2>
            <div class="description">
                如果遇到问题，请检查：<br>
                • 浏览器控制台是否有错误信息<br>
                • D3.js库是否正确加载<br>
                • 网络连接是否正常<br>
                • 数据结构是否符合预期
            </div>
            <button onclick="openConsole()" class="test-button">打开控制台</button>
        </div>
    </div>
    
    <script>
        function openConsole() {
            alert('请按 F12 或右键选择"检查元素"打开开发者工具');
        }
        
        // 添加一些调试信息
        console.log('🧠 ThinkFlash 增强版推演分析测试页面已加载');
        console.log('⏰ 当前时间:', new Date().toLocaleString());
        console.log('🌐 用户代理:', navigator.userAgent);
        
        // 检查本地存储
        const history = localStorage.getItem('thinking-history');
        if (history) {
            console.log('📚 发现历史记录:', JSON.parse(history).length, '条');
        } else {
            console.log('📝 没有历史记录');
        }
        
        // 性能监控
        window.addEventListener('load', () => {
            console.log('⚡ 页面加载完成，耗时:', performance.now().toFixed(2), 'ms');
        });
    </script>
</body>
</html>

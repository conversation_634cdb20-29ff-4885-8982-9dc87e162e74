<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推演分析测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-button {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px 10px 10px 0;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .description {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>ThinkFlash 推演分析功能测试</h1>
    
    <div class="test-section">
        <h2>测试1: 基本推演分析</h2>
        <div class="description">测试推演分析页面的基本功能，包括数据加载和树形图渲染</div>
        <a href="http://localhost:5173/deductive-analysis?topic=人工智能的发展" 
           class="test-button" target="_blank">
            测试基本推演分析
        </a>
    </div>
    
    <div class="test-section">
        <h2>测试2: 带思考提示的推演分析</h2>
        <div class="description">测试带有思考提示参数的推演分析功能</div>
        <a href="http://localhost:5173/deductive-analysis?topic=远程工作的影响&thinkingPrompt=从员工效率和企业管理两个角度分析" 
           class="test-button" target="_blank">
            测试带提示的推演分析
        </a>
    </div>
    
    <div class="test-section">
        <h2>测试3: 复杂主题推演分析</h2>
        <div class="description">测试复杂主题的推演分析，验证时间线功能</div>
        <a href="http://localhost:5173/deductive-analysis?topic=气候变化对全球经济的影响&thinkingPrompt=考虑短期和长期影响，以及不同行业的差异化影响" 
           class="test-button" target="_blank">
            测试复杂主题推演
        </a>
    </div>
    
    <div class="test-section">
        <h2>测试4: 主页面集成测试</h2>
        <div class="description">从主页面生成问题，然后测试推演分析功能</div>
        <a href="http://localhost:5173/" 
           class="test-button" target="_blank">
            打开主页面
        </a>
        <div style="margin-top: 10px; font-size: 12px; color: #888;">
            在主页面输入一个主题，生成问题后，点击问题卡片上的"推演分析"按钮
        </div>
    </div>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <div class="description">
            <strong>预期行为：</strong>
            <ul>
                <li>推演分析页面应该正确加载并显示主题</li>
                <li>应该显示加载状态，然后渲染树形图</li>
                <li>时间线控制面板应该出现在底部</li>
                <li>可以使用前进/后退按钮导航不同的推演步骤</li>
                <li>自动播放功能应该能够逐步展示推演过程</li>
                <li>树形图应该正确显示积极和负面分支</li>
            </ul>
            
            <strong>如果出现问题：</strong>
            <ul>
                <li>打开浏览器开发者工具查看控制台错误</li>
                <li>检查网络请求是否正常</li>
                <li>确认数据结构是否正确</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 添加一些调试信息
        console.log('推演分析测试页面已加载');
        console.log('当前时间:', new Date().toLocaleString());
        
        // 检查本地存储
        const history = localStorage.getItem('thinking-history');
        if (history) {
            console.log('发现历史记录:', JSON.parse(history).length, '条');
        } else {
            console.log('没有历史记录');
        }
    </script>
</body>
</html>
